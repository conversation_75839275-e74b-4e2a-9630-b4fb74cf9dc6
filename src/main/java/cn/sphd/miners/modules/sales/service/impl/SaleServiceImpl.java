package cn.sphd.miners.modules.sales.service.impl;

import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.commodity.dao.PdMerchandiseDao;
import cn.sphd.miners.modules.commodity.dao.PdMerchandiseInvoiceDao;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdMerchandiseInvoice;
import cn.sphd.miners.modules.finance.dao.FinanceInvoiceDetailDao;
import cn.sphd.miners.modules.finance.dao.FinanceInvoiceDetailHistoryDao;
import cn.sphd.miners.modules.finance.entity.FinanceInvoiceDetail;
import cn.sphd.miners.modules.finance.service.InvoiceService;
import cn.sphd.miners.modules.material.dao.SrmSupplierDao;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.sales.dao.*;
import cn.sphd.miners.modules.sales.entity.*;
import cn.sphd.miners.modules.sales.service.ContractBaseService;
import cn.sphd.miners.modules.sales.service.SaleService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.CriteriaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Calendar;

@Service("saleService")
@Transactional
public class SaleServiceImpl implements SaleService {
    @Autowired
    SlOrdersDao slOrdersDao;

    @Autowired
    SrmSupplierDao srmSupplierDao;
    @Autowired
    SlOrdersTeminateDao slOrdersTeminateDao;

    @Autowired
    SlOrdersItemDao itemDao;

    @Autowired
    SlInvoiceApplicationDao slInvoiceApplicationDao;

    @Autowired
    SlOrdersItemInvoiceDao slOrdersItemInvoiceDao;

    @Autowired
    SlInvoiceApplicationItemDao slInvoiceApplicationItemDao;
    @Autowired
    SlOrdersCollectDao collectDao;

    @Autowired
    SlCollectApplicationDao collectApplicationDao;

    @Autowired
    ContractBaseService contractBaseService;

    @Autowired
    PdMerchandiseDao pdMerchandiseDao;

    @Autowired
    PdMerchandiseInvoiceDao pdMerchandiseInvoiceDao;

    @Autowired
    SlContractCommodityDao contractCommodityDao;

    @Autowired
    FinanceInvoiceDetailDao invoiceDetailDao;

    @Autowired
    FinanceInvoiceDetailHistoryDao financeInvoiceDetailHistoryDao;

    @Autowired
    InvoiceService invoiceService;

    @Autowired
    SlOrdersItemInvoiceDao ordersItemInvoiceDao;

    @Autowired
    SlInvoiceApplicationDetailDao invoiceApplicationDetailDao;

    @Override
    public Map getSaleGraph(Integer orderId, Integer oid) {
        String sql = "";
        return slOrdersDao.findMapByConditionNoPage(sql, new Integer[]{orderId, oid});
    }

    @Override
    public List getOcc(Integer orderId) {
        return null;
    }

    @Override
    public Map getDeliveryYet(Integer orderId) {
        String sql = "SELECT\n" +
                "	out_fact,pis.create_date\n" +
                "FROM\n" +
                "	t_pd_inout_stock pis\n" +
                "LEFT JOIN t_sl_orders so ON pis.order_no = so.id WHERE pis.state in(8,9) AND so.id=?0";
        return slOrdersDao.findMapByConditionNoPage(sql, new Integer[]{orderId});
    }

    @Override
    public Map getOrderGoodsAmount(Integer orderId) {
        String hql = "SELECT\n" +
                "	sum(amount) as amount\n" +
                "FROM\n" +
                "	t_sl_orders_item soi\n" +
                "WHERE soi.orders=?0";
        return slOrdersDao.findMapByConditionNoPage(hql, new Integer[]{orderId});
    }

    @Override
    public Map getOccBySalesRelation(Integer oid, Integer orderId, String outerSn, Integer id) {
        Object[] param = new Object[]{oid};
        if (orderId != null)
            param = new Object[]{oid, orderId};
        String sql = "SELECT so.id, so.sign_date,so.create_date, so.sn, (SELECT SUM(amount)-IFNULL(SUM(pis.out_fact),0) FROM t_sl_orders_item WHERE orders = so.id AND sales_relationship = pcp.id) as t_amount, (SELECT SUM(amount) FROM t_sl_orders_item WHERE orders = so.id AND sales_relationship = pcp.id) as amount, so.create_name, so.earliest_delivery_date, so.customer_name, soi.sales_relationship,pcp.inner_sn,pcp.outer_sn,mu.name as unit,soi.delivery_date,sum(pis.out_fact) as out_fact, (SELECT SUM(o.item_quantity) FROM t_sl_orders_item_invoice o LEFT JOIN t_sl_orders o1 ON o.orders = o1.id LEFT JOIN t_sl_invoice_application_item iai1 ON o.application_item = iai1.id LEFT JOIN t_sl_invoice_application ia1 ON iai1.application = ia1.id WHERE o.orders = ia.orders AND IFNULL(ia1.teminate_state, 0) = 0 AND IFNULL(ia1.state, 0) IN (2, 3, 4, 5, 6, 7, 8, 9) AND o.orders_item = soi.id) AS invoice_amount_yet, sum(pis.sign_amount) as sin_amount FROM t_sl_orders so LEFT JOIN t_sl_orders_item soi ON soi.orders = so.id LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id LEFT JOIN t_pd_inout_stock pis ON pis.orders_item = soi.id and pis.state in (8,9) left join t_mt_unit mu on pcp.unit_id = mu.id LEFT JOIN t_sl_invoice_application ia ON ia.orders = soi.orders WHERE so.oid = ?0 and IFNULL(so.out_state,0) not in (5,6) ";
        if(StringUtils.isNotBlank(outerSn)&&!"'null'".equals(outerSn)){
            sql+="AND pcp.outer_sn in (" + outerSn + ")";
        }
        if(id!=null){
            sql+=" AND pcp.id = "+id;
        }
        if (orderId != null)

            sql += "AND so.id != ?1 ";
        sql += " and IFNULL(so.is_scheduled,0) in (2,9) ";

        sql += "GROUP BY so.id HAVING (SUM(soi.amount)-IFNULL(SUM(pis.out_fact),0))!=0";
        return slOrdersDao.findMapByConditionNoPage(sql, param);
    }

    @Override
    public Map getDetailsByInnerSn(String innerSn, Integer oid) {
        String sql = "select distinct pb.id,pb.inner_sn,pb.name,pb.net_weight,pcp.unit,pb.model,pb.minimumi_stock,pb.specifications,ifnull(pcp.technical_principal_name,'') as technical_principal,ifnull(pcp.technical_principal,'') as technical_principal_id from t_pd_base pb LEFT JOIN t_pd_merchandise pcp ON pb.id = pcp.product where pb.inner_sn like '%" + innerSn + "%' and pb.oid=?0";
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{oid});
    }

    @Override
    public Map getCustomerByInnerSn(String innerSn, Integer oid) {
        String sql = "SELECT distinct sc.id,sc.`name`,sc.code from t_sl_customer sc LEFT JOIN t_pd_merchandise pcp ON pcp.customer = sc.id WHERE sc.oid=?0 and sc.type=1";
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{oid});
    }

    @Override
    public Map getDetailsById(Integer id) {
        String sql = "SELECT pcp.id AS pcp_id, scs.id AS customer_id, pcp.outer_sn, pcp.outer_name, pb.inner_sn , pb.`name`, pb.id, pcp.customer_name, scs.NAME, scs.CODE , pcp.technical_principal_name, pcp.process_dept_name, pcp.phrase, pcp.is_contract, pcp.contract_sn , pcp.expiration_date, pcp.signature_date, pcp.is_contain, pb.net_weight, pcp.unit , pb.specifications, pb.model, pcp.has_invoice, pcp.invoice_category, pcp.unit_price_notax , pcp.tax_rate, pcp.unit_price, pcp.is_freight, pcp.minimum_stock AS minimumi_stock, pcp.memo , pcp.create_name, pcp.create_date , ( SELECT COUNT(*) FROM t_pd_merchandise_history pch WHERE pch.product = pcp.id ) AS ed FROM t_pd_base pb LEFT JOIN t_pd_merchandise pcp ON pcp.product = pb.id LEFT JOIN t_sl_customer scs ON scs.id = pcp.customer LEFT JOIN t_pd_merchandise_history pch ON pch.product = pcp.id WHERE pcp.id = ?0";
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{id});
    }

    @Override
    public Map getOuterPdByInnerSn(String innerSn, Object oid) {
        String sql = "SELECT pcp.inner_sn,pcp.outer_name,pcp.outer_sn,sc.`name`,sc.`code`,sc.oid from t_pd_merchandise pcp LEFT JOIN t_sl_customer sc ON pcp.customer = sc.id WHERE pcp.inner_sn=?0 AND sc.oid=?1";
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{innerSn, oid});
    }

    @Override
    public Map getCanBeSent(Integer orderId) {
        String sql = "SELECT DISTINCT\n" +
                "	pb.id as pb_id,pb.current_stock,ms.in_fact,ms.create_date,pb.oid,soi.orders AS order_id,sum(pb.current_stock/soi.amount)/COUNT(DISTINCT soi.sales_relationship) as may\n" +
                "FROM\n" +
                "	 t_sl_orders_item soi\n" +
                "LEFT JOIN t_sl_orders so ON so.id = soi.orders\n" +
                "LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id\n" +
                "LEFT JOIN t_pd_base pb ON pcp.product = pb.id\n" +
                "LEFT JOIN t_mt_stock ms ON ms.productId = pcp.id\n" +
                "WHERE pcp.product IN (SELECT pb.id FROM t_pd_base) AND soi.orders=?0 group by ms.create_date";
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{orderId});
    }

    @Override
    public void orderEnd(Integer orderId, SlOrdersTeminate slOrdersTeminate,Integer outState) {
        SlOrders slOrders = slOrdersDao.get(orderId);
        slOrders.setOutState(outState);
        slOrdersDao.update(slOrders);
        slOrdersTeminate.setOrders(orderId);
        slOrdersTeminateDao.save(slOrdersTeminate);
    }

    @Override
    public Map getOrderByState(Integer state, Integer oid) {
        String sql = "";
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{state, oid});
    }

    @Override
    public Map getItemMapByOrderId(Integer orderId,String orderBy) {
        String sql = "SELECT DISTINCT pcp.id as pcpid,soi.id as sid,soi.model as soi_model,soi.specifications as soi_specifications,soi.sales_relationship as sr_id,soi.progress,soi.delivery_address_id as address_id,( SELECT SUM( o.item_quantity ) FROM t_sl_orders_item_invoice o LEFT JOIN t_sl_orders o1 ON o.orders = o1.id LEFT JOIN t_sl_invoice_application_item iai1 ON o.application_item = iai1.id LEFT JOIN t_sl_invoice_application ia1 ON iai1.application = ia1.id WHERE o.orders = ia.orders AND IFNULL( ia1.teminate_state, 0 )= 0 AND IFNULL( ia1.state, 0 ) IN ( 2, 3, 4, 5, 6, 7, 8, 9 ) AND o.orders_item = soi.id ) AS invoice_amount_yet, soii.item_quantity, soii.item_amount, fid.operate_date, fid.invoice_no, soi.id,soi.create_date as item_create_date,soi.create_name as item_create_name,soi.update_name,soi.update_date, soi.outer_sn, pcp.product, soi.outer_name, pcp.has_invoice, pcp.invoice_category, soi.plan_state, soi.tax_rate, soi.unit_price, soi.unit_price_notax, soi.unit_price_invoice, soi.unit_price_noinvoice, soi.unit_price_reference, so.invoice_require, pb.process_dept_name, pcp.technical_principal_name, pcp.create_name, pb.`name`, pb.inner_sn, pb.id AS pb_id, pcp.unit, pb.model, pb.specifications, pcp.specifications AS out_specifications, pcp.model AS out_model, ifnull( pcp.current_stock, 0 ) AS current_stock, ifnull( pb.current_stock, 0 ) AS pb_current_stock, pcp.minimum_stock AS minimumi_stock, soi.amount,( soi.amount * pcp.unit_price ) AS item_money,( soi.amount * pcp.unit_price_notax ) AS item_notax_money, soi.delivery_date, soi.delivery_address, sca.contact, sca.mobile, so.earliest_delivery_date, soim.lower_quantity, soim.latest_delivery_date, ( SELECT sum( poi.transit_quantity ) FROM t_po_orders_item poi WHERE poi.supplier_material = msa.id ) AS way_num, soim.order_quantity, sum( poi.quantity ) AS po_item_amount, GROUP_CONCAT( DISTINCT po.sn ) AS sn, soim.create_date AS si_create_date, soim.create_name AS si_create_name, sum( pis.out_fact ) AS yet FROM t_sl_orders_item soi LEFT JOIN t_sl_orders so ON soi.orders = so.id LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id LEFT JOIN t_pd_base pb ON pcp.product = pb.id LEFT JOIN t_mt_base mb on mb.product = pb.id LEFT JOIN t_mt_supplier_material msa ON msa.material = mb.id LEFT JOIN t_pd_inout_stock pis ON pis.orders_item = soi.id LEFT JOIN t_pd_out_application poa ON poa.id = pis.applicaion_out LEFT JOIN t_sl_orders_item_invoice soii ON soii.orders = so.id LEFT JOIN t_sl_invoice_application_item iai ON iai.id = soii.application_item LEFT JOIN t_finance_invoice_detail fid ON iai.invoice = fid.id LEFT JOIN t_sl_invoice_application ia ON ia.orders = soi.orders LEFT JOIN t_sl_customer_address sca ON soi.delivery_address_id = sca.id LEFT JOIN t_sl_orders_item_material soim ON soim.item = soi.id LEFT JOIN t_po_orders_item poi ON poi.plan_item = soi.id LEFT JOIN t_po_orders po ON poi.orders = po.id WHERE so.id =?0 GROUP BY soi.id";
//        if(StringUtils.isNotBlank(orderBy))
//            sql+=" order by soi.delivery_date "+orderBy;
//        sql+=" ,MID(pcp.outer_sn,2,10) ASC ";//排序 数字字母在上方
        return itemDao.findMapByConditionNoPage(sql, new Object[]{orderId});
    }

    @Override
    public Map getOrderBase(Integer orderId) {
        String sql = """
                SELECT DISTINCT
                	su.userName AS principal_name,
                	so.version_no,
                	so.production_principal,
                	so.customer_address,
                	so.address AS address_id,
                	so.delivery_type,
                	so.id AS orderId,
                	so.contract_amount,
                	so.has_invoice,
                	so.customer_name,
                	sc.`code`,
                	sc.id,
                	soi.delivery_address,
                	sca.contact,
                	sca.mobile,
                	so.sn,
                	so.sign_date,
                	so.invoice_require,
                	so.create_name,
                	so.in_progress,
                	so.create_date,(
                	SELECT
                		count(*)
                	FROM
                		t_sl_invoice_application ia
                	WHERE
                		ia.orders = so.id
                	) AS application_num,
                	so.operation
                FROM
                	t_sl_orders so
                	LEFT JOIN t_sl_customer sc ON so.customer = sc.id
                	LEFT JOIN t_sl_customer_address sca ON sca.customer = sc.id
                	LEFT JOIN t_sl_orders_item soi ON soi.orders = so.id
                	LEFT JOIN t_sys_user su ON so.production_principal = su.userID
                WHERE
                	so.id = ?0
                	AND sca.id = soi.delivery_address_id
                """;
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{orderId});
    }

    @Override
    public Map getDeliveryAndSignList(Integer id) {
        String sql = "SELECT pis.id, pis.review_time, pis.out_fact, pis.sgin_time, pis.sign_amount, pis.orders_item, pcp.outer_sn,pcp.outer_name,pcp.model,pcp.specifications,mu.name as unit FROM t_pd_inout_stock pis LEFT JOIN t_sl_orders_item soi on soi.id = pis.orders_item LEFT JOIN t_pd_merchandise pcp on soi.sales_relationship = pcp.id LEFT JOIN t_mt_unit mu on pcp.unit_id = mu.id WHERE pis.state IN ( 8, 9 ) AND pis.orders_item = ?0";
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{id});
    }

    @Override
    public Map getSendDetailByOrderId(Integer orderId) {
        String sql = "SELECT pcp.id, pcp.outer_name, pcp.outer_sn, pcp.has_invoice, invoice_category , tax_rate, pcp.unit_price, unit_price_notax , IFNULL(SUM(pis.out_fact), 0) AS out_fact FROM t_sl_orders_item soi LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id LEFT JOIN t_sl_orders so ON so.id = soi.orders LEFT JOIN t_pd_inout_stock pis ON pis.orders_item = soi.id WHERE so.id = ?0 AND pis.state IN (8, 9) GROUP BY soi.id";
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{orderId});
    }

    @Override
    public void saveOrUpdateInvoiceApplication(SlInvoiceApplication application) {
        if (application.getId() == null)
            slInvoiceApplicationDao.save(application);
        else
            slInvoiceApplicationDao.update(application);
    }

    @Override
    public void saveOrdersItemInvoice(SlOrdersItemInvoice itemInvoice) {
        slOrdersItemInvoiceDao.save(itemInvoice);
    }

    @Override
    public void saveOrUpdateInvoiceApplicationItem(SlInvoiceApplicationItem applicationItem) {
        if (applicationItem.getId() == null)
            slInvoiceApplicationItemDao.save(applicationItem);
        else
            slInvoiceApplicationItemDao.update(applicationItem);
    }

    @Override
    public Map getApplicationDetails(Integer oid, Integer userId, Integer orderId,String state,Integer applicationId) {
        String sql ="SELECT DISTINCT fid.id as fid,ia.id, iai.invoice_category, ROUND( iai.amount, 2) AS amount, iai.`line`,fid.line as fid_line, iai.create_date, iai.id AS item_id,fid.operate_date,fid.amount as invoice_amount,invoice_no FROM t_sl_invoice_application_item iai LEFT JOIN t_sl_orders_item_invoice oii ON oii.application_item = iai.id LEFT JOIN t_sl_invoice_application ia ON iai.application = ia.id";

        if(applicationId==null){
            sql +=" LEFT JOIN t_finance_invoice_detail fid on oii.invoice = fid.id";
        }else {
            sql+="  LEFT JOIN t_sl_invoice_application_detail iad on iai.id = iad.application_item LEFT JOIN t_finance_invoice_detail fid on iad.invoice = fid.id";
        }

        sql+=" WHERE ia.org =?0 AND ifnull( teminate_state, 0 ) !=1";

        if(applicationId!=null)
            sql+=" and ia.id = "+applicationId;

        if(userId!=null&&orderId==null)
            sql+="  AND iai.creator ="+userId;

        if(orderId!=null){
            sql+=" AND oii.orders ="+orderId;
        }

        if(StringUtils.isBlank(state))//查询暂存票
            sql+=" AND IFNULL( ia.state, 0 ) IN( 0) ";
        else {//查询已审批通过后财务的暂存票
            sql += " AND IFNULL( ia.state, 0 ) IN(3) AND fid.operation in (0)";
        }
        return slOrdersItemInvoiceDao.findMapByConditionNoPage(sql, new Object[]{oid});
    }

    @Override
    public SlInvoiceApplication getInvoiceApplicationById(Integer applicationId) {
        return slInvoiceApplicationDao.get(applicationId);
    }

    @Override
    public void deleteInvoiceByUserAndState(Integer userID, String state) {
        List<SlInvoiceApplication> applications = slInvoiceApplicationDao.getListByHQL(" from SlInvoiceApplication o where o.creator=?0 and ifnull(o.state,0) in "+state, userID);

        if (applications != null) {

            for (SlInvoiceApplication application : applications) {
                List<SlInvoiceApplicationItem> items = slInvoiceApplicationItemDao.getListByHQL(" from SlInvoiceApplicationItem o where o.application=?0", application.getId());

                if (items != null) {
                    for (SlInvoiceApplicationItem item : items) {
                        List<SlOrdersItemInvoice> itemInvoices = slOrdersItemInvoiceDao.getListByHQL(" from SlOrdersItemInvoice o where o.applicationItem=?0", item.getId());
                        slOrdersItemInvoiceDao.deleteAll(itemInvoices);
                    }
                }
                slInvoiceApplicationItemDao.deleteAll(items);
            }


        }
        slInvoiceApplicationDao.deleteAll(applications);

    }

    @Override
    public Map getApplicationList(User user) {

        String sql = "SELECT ia.id,sc.`name`,iai.amount,ia.create_name,ia.create_date,ia.invoice_count from t_sl_invoice_application_item iai LEFT JOIN t_sl_invoice_application ia ON iai.application=ia.id LEFT JOIN t_sl_orders so ON ia.orders = so.id LEFT JOIN t_sl_customer sc ON so.customer = sc.id where ia.creator=?0";
        return slInvoiceApplicationDao.findMapByConditionNoPage(sql, new Object[]{user.getUserID()});
    }

    @Override
    public Map getApplicationGoods(Integer oid, Integer userID,Integer orderId) {
        String sql = "SELECT DISTINCT soi.id AS item_id, oii.id, oii.orders_item, pb.`name`, soi.amount AS soi_amount, pcp.unit, pcp.unit_price, pcp.outer_name, pcp.unit_price_notax, pcp.has_invoice, pcp.invoice_category, oii.item_quantity, oii.item_price, oii.invoice_rate, oii.invoice_amount,( SELECT SUM( o.item_quantity ) FROM t_sl_orders_item_invoice o LEFT JOIN t_sl_orders o1 ON o.orders = o1.id LEFT JOIN t_sl_invoice_application_item iai1 ON o.application_item = iai1.id LEFT JOIN t_sl_invoice_application ia1 ON iai1.application = ia1.id WHERE o.orders = ia.orders AND ifnull( ia1.state, 0 ) in (5,6,7,8,9) and IFNULL(ia1.teminate_state,0)=0 ) AS invoice_amount_yet, ( SELECT SUM( o.item_quantity ) FROM t_sl_orders_item_invoice o LEFT JOIN t_sl_orders o1 ON o.orders = o1.id LEFT JOIN t_sl_invoice_application_item iai1 ON o.application_item = iai1.id LEFT JOIN t_sl_invoice_application ia1 ON iai1.application = ia1.id WHERE o.orders_item = soi.id AND ifnull( ia1.state, 0 ) in (2,3,4) and IFNULL(ia1.teminate_state,0)=0 ) AS invoice_amount_ing FROM t_sl_orders_item_invoice oii LEFT JOIN t_sl_invoice_application_item iai ON oii.application_item = iai.id LEFT JOIN t_sl_invoice_application ia ON iai.application = ia.id LEFT JOIN t_sl_orders_item soi ON oii.orders_item = soi.id LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id LEFT JOIN t_pd_base pb ON pcp.product = pb.id LEFT JOIN t_sl_orders so ON ia.orders = so.id LEFT JOIN t_sl_orders_item_invoice soii ON soii.orders = so.id WHERE ia.orders = ?0 AND IFNULL( ia.state, 0 ) IN ( 0, 1)";
        return slInvoiceApplicationDao.findMapByConditionNoPage(sql, new Object[]{orderId});
    }

    @Override
    public Map getApplicationGoodsDetails(Integer applicationId, String divisionWay, Integer oid) {

        String sql = "SELECT distinct iad.amount as invoice_quantity,round(fid.amount,2) as fid_amount,fid.amount*(oii.invoice_rate/100) as fid_invoice_amount,oii.id, pb.`name` as pb_name,pcp.outer_name as name, pcp.unit, soi.unit_price, soi.unit_price_notax, soi.unit_price_invoice, soi.unit_price_noinvoice, soi.unit_price_reference, soi.tax_rate, oii.item_quantity, oii.item_price, oii.item_amount, oii.invoice_rate, oii.invoice_amount, CASE so.invoice_require WHEN 1 THEN soi.unit_price_notax WHEN 2 THEN soi.unit_price_invoice WHEN 3 THEN soi.unit_price_noinvoice END as unit_price_c FROM t_sl_orders_item_invoice oii LEFT JOIN t_sl_invoice_application_item iai ON oii.application_item = iai.id LEFT JOIN t_sl_orders_item soi ON oii.orders_item = soi.id LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id LEFT JOIN t_pd_base pb ON pcp.product = pb.id LEFT JOIN t_sl_orders so ON soi.orders = so.id LEFT JOIN t_sl_invoice_application_detail iad on iad.application_item = iai.id LEFT JOIN t_finance_invoice_detail fid on iad.invoice = fid.id  WHERE (iai.application = ?0 or iai.invoice=?1 OR iad.invoice = ?2) and so.oid=?3";
        if(StringUtils.isNotBlank(divisionWay))
            sql = "SELECT DISTINCT iad.amount AS invoice_quantity, round(iad.amount * oii.item_price,2) AS fid_amount, iad.amount * oii.item_price *( oii.invoice_rate / 100 ) AS fid_invoice_amount, oii.id, pb.`name` as pb_name,pcp.outer_name as name, pcp.unit, soi.unit_price, soi.unit_price_notax, soi.unit_price_invoice, soi.unit_price_noinvoice, soi.unit_price_reference, soi.tax_rate, oii.item_quantity, oii.item_price, oii.item_amount, oii.invoice_rate, oii.invoice_amount, CASE so.invoice_require WHEN 1 THEN soi.unit_price_notax WHEN 2 THEN soi.unit_price_invoice WHEN 3 THEN soi.unit_price_noinvoice END AS unit_price_c FROM t_sl_invoice_application_detail iad LEFT JOIN t_sl_invoice_application_item iai ON iad.application_item = iai.id LEFT JOIN t_sl_orders_item_invoice oii ON oii.application_item = iai.id LEFT JOIN t_sl_orders_item soi ON oii.orders_item = soi.id LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id LEFT JOIN t_pd_base pb ON pcp.product = pb.id LEFT JOIN t_sl_orders so ON soi.orders = so.id LEFT JOIN t_finance_invoice_detail fid ON iad.invoice = fid.id WHERE (iai.application = ?0 OR iai.invoice = ?1 OR iad.invoice = ?2) and so.oid=?3 GROUP BY iad.id ";
        return slInvoiceApplicationDao.findMapByConditionNoPage(sql, new Object[]{applicationId,applicationId,applicationId,oid});
    }

    @Override
    public void deleteApplicationById(Integer applicationId) {
        SlInvoiceApplication application = slInvoiceApplicationDao.get(applicationId);

        if (application != null) {
            List<SlInvoiceApplicationItem> items = slInvoiceApplicationItemDao.getListByHQL(" from SlInvoiceApplicationItem o where o.application=?0", application.getId());

            if (items != null) {
                for (SlInvoiceApplicationItem item : items) {
                    List<SlOrdersItemInvoice> itemInvoices = slOrdersItemInvoiceDao.getListByHQL(" from SlOrdersItemInvoice o where o.applicationItem=?0", item.getId());
                    slOrdersItemInvoiceDao.deleteAll(itemInvoices);
                }
            }
            slInvoiceApplicationItemDao.deleteAll(items);
        }

        slInvoiceApplicationDao.delete(application);
    }

    @Override
    public Map getApplicationByState(Integer userId, Integer oid,Integer state,Integer terminateState) {

        String sql = "SELECT DISTINCT so.sn, ia.id, sc.`full_name` AS name, ia.create_name, ia.create_date, CASE IFNULL(ia.division_way,1) WHEN (ia.division_way is null or ia.division_way = 1) THEN ia.invoice_count ELSE '--' END as invoice_count,ia.invoice_count as e_invoice_count, ia.invoice_amount, ia.state,CASE IFNULL( ia.division_way, 1 ) WHEN ( ia.division_way IS NULL OR ia.division_way = 1 ) THEN 1 ELSE 2 END AS division_way  FROM t_sl_invoice_application ia LEFT JOIN t_sl_invoice_application_item iai ON iai.application = ia.id LEFT JOIN t_sl_orders so ON ia.orders = so.id LEFT JOIN t_sl_customer sc ON so.customer = sc.id WHERE so.oid = ?0";

        if (userId!=null&&userId!=0)
            sql+=" and ifnull(ia.creator,0)="+userId;
        if (state!=null)
            sql+=" and ifnull(ia.state,0)="+state;
        else
            sql+="  and ifnull(ia.state,0)!=9";
        if (terminateState!=null){
            sql+=" and ifnull(ia.teminate_state,0)="+terminateState;

            if(terminateState==1)
                sql+=" and DATE_SUB(CURDATE(), INTERVAL 30 DAY) <= ia.create_date and ifnull(ia.teminate_state,0)!=0";
        }

        return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{oid});
    }

    @Override
    public void deleteInvoiceByItemId(Integer ...id) {
        for (Integer i : id){
            slInvoiceApplicationItemDao.deleteById(i);
        }
    }

    @Override
    public void deleteApplication(Integer application) {
        slInvoiceApplicationDao.deleteById(application);
    }

    @Override
    public Map getItemDetailsById(Integer itemId,Integer fid,Integer applicationId) {
        SlInvoiceApplication application = slInvoiceApplicationDao.get(applicationId);
        String sql = "SELECT pcp.id as mer_id,pb.`name` AS pb_name, pb.model,pb.code, pb.specifications, pcp.outer_name AS name,pcp.outer_sn, pcp.unit, ia.division_way, ifnull( CASE WHEN ia.division_way = 2 THEN iad.amount ELSE oii.item_quantity END, oii.item_quantity) AS item_quantity, oii.item_price, ifnull( CASE WHEN ia.division_way = 2 THEN iad.amount * oii.item_price ELSE oii.item_amount END, oii.item_amount ) AS item_amount, ifnull( CASE WHEN ia.division_way = 2 THEN iad.amount * oii.item_price * oii.invoice_rate / 100 ELSE oii.invoice_amount END, oii.invoice_amount ) AS invoice_amount, oi.tax_rate, pcp.invoice_category, iai.id FROM t_sl_orders_item_invoice oii LEFT JOIN t_sl_orders_item oi ON oii.orders_item = oi.id LEFT JOIN t_pd_merchandise pcp ON oi.sales_relationship = pcp.id LEFT JOIN t_pd_base pb ON pcp.product = pb.id LEFT JOIN t_sl_invoice_application_item iai ON oii.application_item = iai.id LEFT JOIN t_sl_invoice_application ia ON iai.application = ia.id LEFT JOIN t_sl_invoice_application_detail iad ON iad.application_item = iai.id AND iad.old_invoice = oii.id LEFT JOIN t_finance_invoice_detail fid ON iad.invoice = fid.id";
        if("1".equals(application.getDivisionWay()))
            sql+=" or oii.invoice = fid.id";

        sql+=" where iai.id = ?0";


        if("2".equals(application.getDivisionWay())&&fid!=null)
            sql+=" and fid.id="+fid;
//        if("2".equals(application.getDivisionWay()))
//            sql+=" group by iad.id";
        Map map = slInvoiceApplicationItemDao.findMapByConditionNoPage(sql,new Object[]{itemId});
        ArrayList<HashMap> datas = (ArrayList) map.get("data");
        if(datas!=null&&datas.size()>0)
            for (HashMap m : datas) {
                Integer id = (Integer) m.get("mer_id");
                PdMerchandise p = pdMerchandiseDao.get(id);
                if (p != null) {
                    List<PdMerchandiseInvoice> merchandiseInvoices = pdMerchandiseInvoiceDao.getListByHQL(" from PdMerchandiseInvoice o where o.merchandise=?0",id);
//                    m.put("merchandiseInvoices",merchandiseInvoices);

                    //设置一下
                    if (merchandiseInvoices != null&& !merchandiseInvoices.isEmpty()) {
                        String codeAndName = PdMerchandise.getInvoiceByType(1,p,merchandiseInvoices);
                        String modelAndSpec = PdMerchandise.getInvoiceByType(2,p,merchandiseInvoices);

                        m.put("codeAndName",codeAndName);
                        m.put("modelAndSpec",modelAndSpec);
                    }

                }


            }

        return map;
    }

    @Override
    public Map getBaseDetailsByApplicationId(Integer applicationId,Integer itemId,Integer fid) {
        String sql = "select distinct ia.division_way, ic.check_result, idi.create_name as idi_create_name, ia.creator, sc.id as customer_id, case when ia.division_way=2 then iad.invoice else fid.id end as invoice_id, fid.invoice_no, fid.audit_date as operate_date, fid.reason, fid.auditor_name, fid.audit_date, iai.invoice_category, case IFNULL(ia.division_way, 1) when (ia.division_way is null or ia.division_way = 1) then ia.invoice_count else '--' end as invoice_count, ia.invoice_count as e_invoice_count, iai.amount as invoice_amount, ia.create_name, ia.create_date, sc.full_name name, ia.state, ia.updator, ia.update_name, ia.update_date, ia.teminate_time, ia.teminater_name, ia.teminate_state, ia.teminate_reason,sc.invoice_name,sc.invoice_address,sc.taxpayerID,sc.bank_no,sc.bank_code,sc.bank_name,sc.telephone from t_sl_invoice_application ia left join t_sl_invoice_application_item iai on iai.application = ia.id left join t_sl_orders_item_invoice oii on oii.application_item = iai.id left join t_sl_invoice_application_detail iad on iad.application_item = iai.id and iad.old_invoice = oii.id left join t_finance_invoice_detail fid on (iai.invoice = fid.id or iad.invoice = fid.id) left join t_sl_orders so on ia.orders = so.id left join t_sl_customer sc on so.customer = sc.id left join t_sl_invoice_delivery sid on sid.application = ia.id left join t_sl_invoice_delivery_item idi on idi.delivery = sid.id left join t_sl_invoice_check as ic on ic.invoice = iai.invoice where ia.id=?0";
        if(itemId!=null)
            sql+=" and iai.id="+itemId;
        if(fid!=null)
            sql+=" and fid.id = "+fid;
        return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{applicationId});
    }

    @Override
    public Map getTerminateApplication(Integer oid) {
        String sql = "SELECT distinct ia.id,sc.`name`,ia.create_name,ia.create_date,ia.invoice_count,ia.invoice_amount,ia.state from t_sl_invoice_application ia LEFT JOIN t_sl_invoice_application_item iai ON iai.application=ia.id LEFT JOIN t_sl_orders so ON ia.orders = so.id LEFT JOIN t_sl_customer sc on so.customer = sc.id where so.oid=?0";
        return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{oid});
    }

    @Override
    public Map getInvoicedList(Integer id,Integer ordersItem,String state,String losted) {
        // 根据division_way的值使用不同的查询逻辑，避免使用UNION ALL
        String sql;

        // 构建通用的过滤条件
        String commonConditions = "";
        if(StringUtils.isNotBlank(state))
            commonConditions += " AND ifnull(ia.state,0) in "+state;
        if (StringUtils.isNotBlank(losted))
            commonConditions += " AND IFNULL(fid.losted,0) not in "+losted;
        if(ordersItem!=null)
            commonConditions += " AND oii.orders_item="+ordersItem;
        if(id!=null)
            commonConditions += " AND ia.orders="+id;
        commonConditions += " AND ifnull(ia.teminate_state,0)=0";

        // 修正发票号过滤条件，避免00000000被过滤掉
        if("(5,6,7,8,9)".equals(state))
            commonConditions += " AND fid.invoice_no IS NOT NULL AND fid.invoice_no != ''";

        // 使用统一的查询，通过LEFT JOIN和条件来处理不同的division_way情况
        sql = "SELECT " +
                "CASE " +
                "  WHEN IFNULL(ia.division_way, '1') != '2' THEN iai.amount " +
                "  ELSE fid.amount " +
                "END AS item_amount, " +
                "oii.id, " +
                "CASE " +
                "  WHEN IFNULL(ia.division_way, '1') != '2' THEN oii.item_quantity " +
                "  ELSE iad.amount " +
                "END as item_quantity, " +
                "oii.invoice_time, " +
                "oii.orders_item, " +
                "oii.item_amount as amount, " +
                "oii.invoice_amount, " +
                "fid.invoice_no, " +
                "fid.operate_date " +
                "FROM t_sl_orders_item_invoice oii " +
                "LEFT JOIN t_sl_invoice_application_item iai ON oii.application_item = iai.id " +
                "LEFT JOIN t_sl_invoice_application ia ON iai.application = ia.id " +
                "LEFT JOIN t_sl_invoice_application_detail iad ON iad.application = ia.id AND ia.division_way = '2' AND iad.application_item = iai.id " +
                "LEFT JOIN t_finance_invoice_detail fid ON " +
                "  ((IFNULL(ia.division_way, '1') != '2' AND (iai.invoice = fid.id OR oii.invoice = fid.id)) " +
                "   OR (ia.division_way = '2' AND iad.invoice = fid.id)) " +
                "WHERE 1=1" +
                commonConditions;

        return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{});
    }

    @Override
    public Map  getInvoiceMapByOrderId(Integer orderId, String s) {
        String sql = "SELECT soc.item_amount, soc.create_date, sca.method,sca.return_no FROM t_sl_orders_collect soc LEFT JOIN t_sl_collect_application sca ON soc.application = sca.id WHERE soc.orders = ?0";
        return slInvoiceApplicationItemDao.findMapByConditionNoPage(sql,new Object[]{orderId});
    }

    @Override
    public List<Map<String, Object>>getOrdersByCustomerAndOid(Integer customer, Integer oid) {
        String sql = "SELECT\n" +
                "so.id,\n" +
                "so.sn,\n" +
                "so.oid,\n" +
                "so.contract_amount contractAmount,\n" +
                "sop.collected_percent collectedPercent,\n" +
                "(select SUM(soc.item_amount) from t_sl_orders_collect soc where soc.orders=so.id) amount,\n" +
                "so.customer\n" +
                "FROM\n" +
                "t_sl_orders so\n" +
                "LEFT JOIN t_sl_orders_percent sop ON sop.orders = so.id\n" +
                "LEFT JOIN t_sl_customer sc ON so.customer = sc.id  where  so.oid = "+oid+"  and ifnull(out_state,0)!=6 and so.customer="+customer+" and IFNULL(sop.collected_percent,0)<1 GROUP BY so.id";
        return slOrdersDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public Map getReOrdersByOid(Integer oid) {

        String sql = "SELECT ca.id,sc.name,sc.full_name fullName,sc.id as customer_id,sc.code as customer_code,ca.amount,sum(soc.item_amount) as item_amount,ca.create_name,ca.create_date,ca.update_date,ca.saler_time,ca.finance_time,ca.financer_name,ca.approval_date,ca.saler_name,apply_date,ca.receive_date,ca.return_no,ca.original_corp,ca.expire_date,ca.bank_name,ca.expect_begin_date,ca.record_type,ca.method from t_sl_collect_application ca LEFT JOIN t_sl_customer sc on sc.id = ca.customer left join t_sl_orders_collect soc on soc.application = ca.id WHERE ifnull(ca.orders_state,0)=?0 and ca.org=?1 group by ca.id";
        return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{2,oid});
    }

    @Override
    public Map getCollectDetailsById(Integer id) {
        String sql = "select ca.expect_begin_date,ca.expect_end_date,ca.saler_name as so_create_name,ca.update_date,ca.saler_time,ca.finance_time,ca.record_type,ca.id,sc.name,ca.amount,ca.method,ca.receive_date,ca.return_no,ca.original_corp,ca.expire_date,ca.bank_name,ca.create_date,ca.create_name,ca.financer_name,ca.approval_date from t_sl_collect_application ca LEFT JOIN t_sl_customer sc on sc.id = ca.customer left join t_sl_orders_collect soc on soc.application = ca.id left join t_sl_orders so on so.id = soc.orders where ca.id=?0";
        return slOrdersDao.findMapByConditionNoPage(sql,new Object[]{id});
    }

    @Override
    public Map getCollectedOrders(Integer id) {
        String sql = "SELECT DISTINCT SUM(soc.item_amount) as soc_item_amount,so.id, so.sn, so.contract_amount, oc.item_amount, op.collected_percent , ca.amount FROM t_sl_collect_application ca LEFT JOIN t_sl_customer sc ON sc.id = ca.customer LEFT JOIN t_sl_orders_collect oc ON oc.application = ca.id LEFT JOIN t_sl_orders so ON oc.orders = so.id LEFT JOIN t_sl_orders_percent op ON op.orders = so.id left join t_sl_orders_collect soc on soc.orders = so.id where ca.id=?0 and ifnull(so.id,0)!=0 GROUP BY so.id";
        return slOrdersDao.findMapByConditionNoPage(sql,new Object[]{id});
    }

    @Override
    public Map getCollectedAmountByOrderId(Integer orderId) {
        String sql = "select * from t_sl_orders_collect soc where soc.orders=?0";
        return slOrdersDao.findMapByConditionNoPage(sql,new Object[]{orderId});
    }

    @Override
    public SlOrdersCollect getOrdersCollectById(Integer collectId) {
        return collectDao.get(collectId);
    }

    @Override
    public void saveOrUpdateOrdersCollect(SlOrdersCollect collect) {
        if(collect.getId()!=null)
            collectDao.update(collect);
        else
            collectDao.save(collect);
    }

    @Override
    public List<SlOrdersCollect> getOrdersCollectsByApplicationId(Integer applicationId) {
        String hql = " from SlOrdersCollect o where o.application=?0";
        return collectDao.getListByHQL(hql,applicationId);
    }

    @Override
    public List<SlCollectApplication> getSlCollectApplicationsByCustomerId(Integer id) {
       String hql = " from SlCollectApplication o where o.customer = ?0";
       return collectApplicationDao.getListByHQL(hql,id);
    }

    @Override
    public List<SlOrdersCollect> getOrdersCollectsByCustomerId(Integer id) {
        String hql = " from SlOrdersCollect o where o.customer = ?0";
        return collectDao.getListByHQL(hql,id);
    }

    @Override
    public List<SlInvoiceApplicationItem> getInvoiceApplicationItemsByApplicationId(Integer id) {

        String hql = " from SlInvoiceApplicationItem o where o.application="+id;
        return slInvoiceApplicationItemDao.getListByHQL(hql);
    }

    @Override
    public Map getInvoiceMapByOrderIdAndState(Integer orderId, String state) {
        String sql = "SELECT SUM(iai.invoice_amount) as money from t_finance_invoice_detail fid LEFT JOIN t_sl_invoice_application_item iai on iai.invoice = fid.id LEFT JOIN t_sl_invoice_application ia ON iai.application=ia.id WHERE IFNULL(ia.teminate_state,0)=0 AND ia.orders=?0";
        if(StringUtils.isNotBlank(state))
            sql+= " and IFNULL(ia.state,0) in "+state;
        return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{orderId});
    }

    @Override
    public Map getExceptionInvoices(Object oid,Integer orderId) {
        String sql = "SELECT fid.operate_date, fid.invoice_no, fid.amount, fid.losted, idi.sign_handle, idi.sign_result, idi.sign_error,fi.org,ia.orders FROM t_finance_invoice_detail fid LEFT JOIN t_sl_invoice_delivery_item idi ON idi.invoice = fid.id LEFT JOIN t_finance_invoice fi ON fid.invoice_reg = fi.id LEFT JOIN t_sl_invoice_application_item iai ON iai.invoice = fid.id LEFT JOIN t_sl_invoice_application ia ON iai.application = ia.id WHERE ( IFNULL( idi.sign_result, 0 ) = 2 OR IFNULL( fid.losted, 0 ) != 0 ) AND ia.org=?0 AND ia.orders=?1";
        return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{oid,orderId});
    }

    @Override
    public Integer getInvoiceApplicationCount(Integer oid,String state,String noState,Integer userId) {
        String countSql = "SELECT COUNT(id) as count FROM (";

        String sql = "SELECT distinct ia.id,sc.`name`,ia.create_name,ia.create_date,ia.invoice_count,ia.invoice_amount,ia.state from t_sl_invoice_application ia LEFT JOIN t_sl_invoice_application_item iai ON iai.application=ia.id LEFT JOIN t_sl_orders so ON ia.orders = so.id LEFT JOIN t_sl_customer sc on so.customer = sc.id where so.oid =?0 and ifnull(teminate_state,0)=0";
        if(userId!=null)
            sql+=" and ia.creator="+userId;
        if(state!=null)
            sql+=" and ia.state in "+state;
        if(noState!=null)
            sql+=" and ia.state not in "+noState;

        countSql = countSql+sql+") as count";
        Map map = slInvoiceApplicationDao.findMapByConditionNoPage(countSql,new Object[]{oid});

        ArrayList list  = (ArrayList) map.get("data");
        if(list!=null&&list.size()>0){
            HashMap m = (HashMap) list.get(0);
            return Integer.valueOf(m.get("count").toString());
        }
        return null;
    }

    @Override
    public SlInvoiceApplicationItem getInvoiceApplicationItemById(Integer id) {
        return slInvoiceApplicationItemDao.get(id);
    }

    @Override
    public Map getApplicationItemListByApplicationId(Integer applicationId) {
        String sql = "";
        SlInvoiceApplication application = slInvoiceApplicationDao.get(applicationId);
        if((StringUtils.isBlank(application.getDivisionWay())||"1".equals(application.getDivisionWay()))||!"5".equals(application.getState())){//由本人决定
            sql = "SELECT iai.id,iai.amount,iai.line,iai.invoice_category,fid.invoice_no,fid.audit_date, fid.operate_date,ic.check_result from t_sl_invoice_application_item iai left join t_finance_invoice_detail fid on iai.invoice=fid.id LEFT JOIN t_sl_invoice_check ic on ic.invoice = fid.id WHERE iai.application=?0";
        }else {//由财务决定且已选择发票后
            sql = "SELECT iai.id,oii.id as oii_id,fid.id as fid, fid.amount, iai.line, iai.invoice_category, fid.invoice_no, fid.audit_date,fid.operate_date, ic.check_result FROM t_sl_invoice_application_detail iad LEFT JOIN t_sl_invoice_application ia ON iad.application = ia.id LEFT JOIN t_sl_invoice_application_item iai ON iad.application_item = iai.id LEFT JOIN t_finance_invoice_detail fid ON iad.invoice = fid.id LEFT JOIN t_sl_invoice_check ic ON ic.invoice = fid.id LEFT JOIN t_sl_orders_item_invoice oii on iad.application_item = oii.id WHERE ia.id = ?0 GROUP BY iad.invoice";
        }
        return slInvoiceApplicationItemDao.findMapByConditionNoPage(sql,new Object[]{applicationId});
    }


    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer result = null;
        switch (code) {
            //销售开票部分
            case "invoiceApply": //开票申请
                result = this.getInvoiceApplicationCount(user.getOid(), "(2)", "(9)", user.getUserID());
                break;
            case "invoiceApproval": //开票审批
                result = this.getInvoiceApplicationCount(user.getOid(), "(2,4)", "(9)", null);
                break;
            case "invoiceRegister": //开票登记
                if("finance".equals(user.getManagerCode())) {
                    result = this.getInvoiceApplicationCount(user.getOid(), "(3)", "(9)", null);
                }else {
                    result = 0;
                }
                break;
        }
        return result;
    }



    @Override
    public Map getProductPriceHistory(Integer id) {
        String sql = "SELECT so.id,so.invoice_require, so.create_date, pcp.id as pdid, pcp.outer_name, soi.unit_price , soi.unit_price_notax, soi.unit_price_invoice, soi.unit_price_noinvoice, soi.unit_price_reference,soi.tax_rate  FROM t_sl_orders so LEFT JOIN t_sl_orders_item soi ON soi.orders = so.id LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id WHERE pcp.id = ?0 ORDER BY so.create_date desc LIMIT 0,5";
        return slOrdersDao.findMapByConditionNoPage(sql,new Object[]{id});
    }

    @Override
    public Map getCustomerInvoice(Integer id) {
        String sql = "SELECT sc.id, sc.oid, sc.`code`, sc.`invoice_name` as name, sc.full_name , sc.bank_no, sc.bank_code, sc.bank_name, sc.invoice_address as address, sc.telephone , sc.taxpayerID, cis.invoice_require,sc.create_date,sc.create_name,sc.update_date,sc.update_name FROM t_sl_customer sc LEFT JOIN t_sl_customer_invoice_setting cis ON cis.customer = sc.id where sc.id=?0";
        return slOrdersDao.findMapByConditionNoPage(sql,new Object[]{id});
    }

    @Override
    public Map getInvoicedAndDeliveryList(Integer orderId, Integer ordersItem, String state, String losted,Integer id) {
        // 根据division_way的值使用不同的查询逻辑，避免使用UNION ALL
        String sql;

        // 构建通用的过滤条件
        String commonConditions = "";
        if(StringUtils.isNotBlank(state))
            commonConditions += " AND ifnull(ia.state,0) in "+state;
        if (StringUtils.isNotBlank(losted))
            commonConditions += " AND IFNULL(fid.losted,0) not in"+losted;
        if(ordersItem!=null)
            commonConditions += " AND soii.orders_item="+ordersItem;
        if(orderId!=null)
            commonConditions += " AND ia.orders="+orderId;
        if (id!=null){//商品id
            commonConditions += " AND soi.sales_relationship ="+id;
        }
        commonConditions += " AND ifnull(ia.teminate_state,0)=0";

        // 使用统一的查询，通过LEFT JOIN和条件来处理不同的division_way情况
        sql = "SELECT ia.orders, " +
                "CASE " +
                "  WHEN IFNULL(ia.division_way, '1') != '2' THEN iai.amount " +
                "  ELSE fid.amount " +
                "END AS invoice_amount, " +
                "fid.invoice_no, " +
                "ia.state AS invoice_state, " +
                "fid.operate_date AS invoice_date, " +
                "sid.id AS delivery_id, " +
                "sid.delivery_way AS method, " +
                "sid.delivery_time AS delivery_date, " +
                "sid.receiver AS re_name, " +
                "sid.sign_result, " +
                "COALESCE(soi.unit_price, 0) AS unit_price, " +
                "COALESCE(soii.item_quantity, 0) AS item_quantity, " +
                "COALESCE(soii.item_unit, '') AS item_unit " +
                "FROM t_sl_invoice_application ia " +
                "LEFT JOIN t_sl_invoice_application_item iai ON iai.application = ia.id AND IFNULL(ia.division_way, '1') != '2' " +
                "LEFT JOIN t_sl_invoice_delivery sid ON sid.application = ia.id " +
                "LEFT JOIN t_sl_orders_item_invoice soii ON soii.application_item = iai.id " +
                "LEFT JOIN t_sl_orders_item soi ON soii.orders_item = soi.id " +
                "LEFT JOIN t_sl_invoice_application_detail iad ON iad.application = ia.id AND ia.division_way = '2' " +
                "LEFT JOIN t_finance_invoice_detail fid ON " +
                "  ((IFNULL(ia.division_way, '1') != '2' AND (iai.invoice = fid.id OR soii.invoice = fid.id)) " +
                "   OR (ia.division_way = '2' AND iad.invoice = fid.id)) " +
                "WHERE 1 = 1 AND fid.invoice_no IS NOT NULL AND fid.invoice_no != ''" +
                commonConditions;

        return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{});
    }


    @Override
    public Map getCollectedMapByOrderId(Integer orderId) {
        String sql = "select soa.method,soc.item_amount,soa.receive_date,soa.receive_bank,soa.return_no,soa.payer,soa.original_corp,soa.bank_name,soa.expire_date,soa.apply_date,soa.financer_name,soa.financer_time,soa.create_date,soa.create_name from t_sl_orders_collect soc left join t_sl_collect_application soa on soc.application = soa.id";
        if(orderId!=null)
            sql+=" where soc.orders=?0";
        return slOrdersDao.findMapByConditionNoPage(sql,new Object[]{orderId});
    }

    @Override
    public Map getCollectOrdersByApplicationId(Integer id) {
        String sql = "SELECT so.id,so.sn,soc.item_amount from t_sl_orders_collect soc LEFT JOIN t_sl_collect_application soa ON soc.application = soa.id LEFT JOIN t_sl_orders so ON soc.orders = so.id where soa.id=?0";
        return slOrdersDao.findMapByConditionNoPage(sql,new Object[]{id});
    }

    @Override
    public Map getCollectedAmountByOrderIdOrOid(Integer orderId, Integer oid,Integer customerId) {
        String sql = "select soc.* from t_sl_orders_collect soc left join t_sl_orders so on soc.orders = so.id where 1=1";

        if(orderId!=null)
            sql+= " and soc.orders="+orderId;
        if(oid!=null)
            sql+=" and so.oid = "+oid;
        if(customerId!=null)
            sql += " and so.customer = "+customerId;

        return slOrdersDao.findMapByConditionNoPage(sql,new Object[]{});
    }

    @Override
    public Map getApplicationDetail(Integer applicationId) {
      String sql = "SELECT distinct oii.id AS item_id, iai.creator, oii.id, pb.`name` as pb_name, pb.`code` as pb_code,pcp.outer_name as name,pcp.outer_sn as code, pcp.unit, soi.amount, oii.item_quantity, oii.item_price, oii.item_amount, oii.invoice_rate, oii.invoice_amount, oii.invoice, COUNT( DISTINCT iad.amount ) AS invoice_yet, GROUP_CONCAT( distinct iad.id ) AS iads, IFNULL(SUM( iad.amount ),0) AS invoice_yet_amount, CASE so.invoice_require WHEN 1 THEN soi.unit_price_notax WHEN 2 THEN soi.unit_price_invoice WHEN 3 THEN soi.unit_price_noinvoice END AS unit_price_c FROM t_sl_orders_item_invoice oii LEFT JOIN t_sl_invoice_application_item iai ON oii.application_item = iai.id LEFT JOIN t_sl_orders_item soi ON oii.orders_item = soi.id LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id LEFT JOIN t_pd_base pb ON pcp.product = pb.id LEFT JOIN t_sl_orders so ON soi.orders = so.id LEFT JOIN t_sl_invoice_application_detail iad on iad.old_invoice = oii.id LEFT JOIN t_finance_invoice_detail fid ON iad.invoice = fid.id WHERE iai.application = ?0  GROUP BY oii.id";
      return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{applicationId});
    }

    @Override
    public Map getApplicationBase(Integer applicationId) {
        String sql = "SELECT DISTINCT ia.id AS application_id, so.id AS order_id, sc.id, sc.`name`, sc.`code`, so.sn, so.invoice_require, ia.creator, ia.create_name, ia.create_date, ia.update_name, ia.update_date, ia.invoice_amount, ia.state AS application_state, Round(SUM( iad.amount*oii.item_price + iad.amount*oii.item_price*oii.invoice_rate/100),2) AS fid_all_amount, COUNT( DISTINCT iad.invoice ) AS fid_count FROM t_sl_invoice_application ia LEFT JOIN t_sl_invoice_application_item iai ON iai.application = ia.id LEFT JOIN t_sl_orders_item_invoice oii ON oii.application_item = iai.id LEFT JOIN t_sl_orders_item soi ON oii.orders_item = soi.id LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id LEFT JOIN t_pd_base pb ON pcp.product = pb.id LEFT JOIN t_sl_orders so ON soi.orders = so.id LEFT JOIN t_sl_customer sc ON so.customer = sc.id LEFT JOIN t_sl_invoice_application_detail iad ON iad.old_invoice = oii.id LEFT JOIN t_finance_invoice_detail fid ON iad.invoice = fid.id WHERE ia.id = ?0";

        return slInvoiceApplicationDao.findMapByConditionNoPage(sql,new Object[]{applicationId});
    }

    @Override
    public Integer delInvoiceDetail(Integer[] id) {
        //删除关联关系
        for (Integer i : id){
            SlInvoiceApplicationDetail detail = invoiceApplicationDetailDao.getByHQL(" from SlInvoiceApplicationDetail o where o.invoice = ?0",i);

            if(detail!=null){
                invoiceService.deleteUpdateById(i);  //删除某条时，将数据清空
                //关联的发票置空
                invoiceApplicationDetailDao.delete(detail);
            }

            //slInvoiceApplicationItemDao.update(item);
        }

        return 1;
    }

    @Override
    public SlOrdersItemInvoice getOrdersItemInvoiceByApplicationItem(Integer id) {
        String hql = " from SlOrdersItemInvoice o where o.applicationItem="+id;
        return ordersItemInvoiceDao.getByHQL(hql);

    }

    @Override
    public Integer editInvoiceDetail(User user,Integer itemId, Integer oldFid, Integer newFid,String date,String operation,Boolean choose) {
        if (choose==null) choose = true;

        FinanceInvoiceDetail old = invoiceService.getFinanceInvoiceDetailById(oldFid);//之前的发票
        FinanceInvoiceDetail newF = invoiceService.getFinanceInvoiceDetailById(newFid);//选择的新发票
        if(Objects.equals(oldFid, newFid)){//仅修改发票日期
            old.setOperateDate(NewDateUtils.dateFromString(date,"yyyy-MM-dd"));
            invoiceService.updateFinanceInvoiceDetail(newF);
            return 1;
        }
        List<SlInvoiceApplicationDetail> invoiceApplicationDetails = invoiceApplicationDetailDao.getListByHQL(" from SlInvoiceApplicationDetail o where o.applicationItem=?0 and o.invoice=?1",itemId,oldFid);


        newF.setOperation(StringUtils.isBlank(operation)?"0":operation);//当前为暂存操作
        invoiceService.updateFinanceInvoiceDetail(newF);

        newF.setState("2");
        newF.setSource("2");

        newF.setAmount( old.getAmount());
        newF.setOperateDate(NewDateUtils.dateFromString(date,"yyyy-MM-dd"));
        newF.setLines(old.getLine());
        invoiceService.updateFinanceInvoiceDetail(newF);

        if (choose)
        invoiceService.chooseInvoice(newFid, old.getLine(), new Date(), newF.getAmount(), old.getReceiveCorp(), old.getApplicantName(), old.getApplicant(), new Date(), old.getReceiveCorpId(), user,"2");

        invoiceService.deleteUpdateById(oldFid);

        if(invoiceApplicationDetails!=null&&invoiceApplicationDetails.size()>0){
            for (SlInvoiceApplicationDetail invoiceApplicationDetail : invoiceApplicationDetails){
                SlInvoiceApplicationDetail newDetail = new SlInvoiceApplicationDetail();
                BeanUtils.copyProperties(invoiceApplicationDetail,newDetail);//辅助老的关联关系

                newDetail.setInvoice(newFid);
                newDetail.setId(null);

                invoiceApplicationDetailDao.delete(invoiceApplicationDetail);//删除老的
                invoiceApplicationDetailDao.save(newDetail);//创建新的
            }
        }

        return 1;
    }

    @Override
    public void changeOperationStateByApplicationItemIds(String applicationItemIds) {
        for (String id : applicationItemIds.split(",")){
            List<SlInvoiceApplicationDetail> invoiceApplicationDetails = invoiceApplicationDetailDao.getListByHQL(" from SlInvoiceApplicationDetail o where o.applicationItem=?0",Integer.valueOf(id));
            for (SlInvoiceApplicationDetail detail : invoiceApplicationDetails){
                if(detail!=null){
                    FinanceInvoiceDetail financeInvoiceDetail = invoiceService.getFinanceInvoiceDetailById(detail.getInvoice());
                    if(financeInvoiceDetail!=null){
                        financeInvoiceDetail.setOperation("1");
                        invoiceService.updateFinanceInvoiceDetail(financeInvoiceDetail);
                    }
                }
            }

        }
    }

    @Override
    public List<SlInvoiceApplicationDetail> getInvoiceApplicationDetailByApplicationId(Integer id) {
        return invoiceApplicationDetailDao.getListByHQL(" from SlInvoiceApplicationDetail o where o.application = ?0",id);
    }

    @Override
    public SlOrdersItemInvoice getOrdersItemInvoiceById(Integer oiiId) {
        return ordersItemInvoiceDao.get(oiiId);
    }

    @Override
    public void updateOrdersItemInvoice(SlOrdersItemInvoice ordersItemInvoice) {
        ordersItemInvoiceDao.update(ordersItemInvoice);
    }

    @Override
    public SlInvoiceApplicationDetail getInvoiceApplicationDetailByItemIdAndInvoice(int itemId, Integer invoiceId) {
        return invoiceApplicationDetailDao.getByHQL(" from SlInvoiceApplicationDetail o where o.applicationItem=?0 and o.invoice=?1",itemId,invoiceId);
    }

    @Override
    public void test() {
        String sql1 = " select * from t_mt_supplier";

        Map t1 = invoiceDetailDao.findMapByConditionNoPage(sql1,new Object[]{});
        ArrayList<HashMap> a = (ArrayList) t1.get("data");


        String hql2 = " from SrmSupplier o where 1=1";
        List<SrmSupplier> srmSuppliers = srmSupplierDao.getAll();

        for (SrmSupplier s : srmSuppliers){
            for (HashMap b : a) {
                if(Objects.equals(s.getId(),b.get("id"))){
                    s.setOrg((Integer) b.get("oid"));
                    System.out.println("赋值");
                }
            }

            srmSupplierDao.update(s);
        }
        System.out.println(111);
    }

    @Override
    public Map getDeliveryAndSignListByOrderId(Integer orderId, Integer i) {
        String sql = "SELECT pis.id, pis.review_time, pis.out_fact, pis.sgin_time, pis.sign_amount, pis.orders_item, pcp.outer_sn,pcp.outer_name,pcp.model,pcp.specifications,mu.name as unit FROM t_pd_inout_stock pis LEFT JOIN t_sl_orders_item soi on soi.id = pis.orders_item LEFT JOIN t_pd_merchandise pcp on soi.sales_relationship = pcp.id LEFT JOIN t_mt_unit mu on pcp.unit_id = mu.id WHERE pis.state IN ( 8, 9 ) AND pis.orders_no = ?0";
        if (i!=null){
            sql += " AND pis.sgin_time is not null";
        }
        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{orderId});
    }

    @Override
    public Map getOccItemsByOrderId(Integer id) {
        String sql = "select so.id,\n" +
                "       so.sn,\n" +
                "       sum(pis.out_fact) as out_fact,\n" +
                "       so.sign_date as create_date,so.create_name,so.customer_name,\n" +
                "       soi.delivery_date,pcp.unit,soi.amount\n" +
                "\n" +
                "from t_sl_orders_item soi\n" +
                "         left join t_sl_orders so on soi.orders = so.id\n" +
                "         LEFT JOIN t_pd_merchandise pcp ON soi.sales_relationship = pcp.id\n" +
                "         LEFT JOIN t_pd_inout_stock pis ON pis.orders_item = soi.id and pis.state in (8, 9)\n" +
                "WHERE  IFNULL(so.out_state, 0) not in (5, 6) ";
        sql+=" and pcp.id="+id;
        sql+=" GROUP BY soi.id HAVING (SUM(soi.amount)-IFNULL(SUM(pis.out_fact),0))!=0 ";

        return slOrdersDao.findMapByConditionNoPage(sql,new Object[]{});
    }

    @Override
    public Map getAnnualSalesAmountByCondition(Integer productId, String beginDate, String endDate) {
        // 如果未提供 beginDate 和 endDate，使用当前年份的开始日期和结束日期
        if (beginDate == null || endDate == null) {
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);


            calendar.set(currentYear, Calendar.JANUARY, 1, 0, 0, 0);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            beginDate = sdf.format(calendar.getTime());


            calendar.set(currentYear, Calendar.DECEMBER, 31, 23, 59, 59);
            endDate = sdf.format(calendar.getTime());
        }

        String sql = """
                SELECT
                    pm.product,SUM(CASE
                    so.invoice_require
                    WHEN 1 THEN
                    ( soi.unit_price * soi.amount )
                    WHEN 2 THEN
                    ( soi.unit_price_invoice * soi.amount )
                    WHEN 3 THEN
                    ( soi.unit_price_noinvoice * soi.amount )
                    END) AS amount
                FROM
                    t_sl_orders_item soi
                LEFT JOIN t_sl_orders so ON soi.orders = so.id
                LEFT JOIN t_pd_merchandise pm ON soi.sales_relationship = pm.id
                WHERE pm.product = ?0
                AND so.create_date BETWEEN ?1 AND ?2
                AND so.is_scheduled = 2
                GROUP BY MONTH(so.create_date)
                """;

        return slOrdersDao.findMapByConditionNoPage(sql, new Object[]{productId, beginDate, endDate});
    }

    @Override
    public List<Map> getAnnualSalesAmountByOrg(Integer org, Integer year) {
        // 如果未提供 beginDate 和 endDate，使用当前年份的开始日期和结束日期

        String sql = """
                SELECT
                	soi.sales_relationship as id,
                	SUM(
                	CASE
                			so.invoice_require\s
                			WHEN 1 THEN
                			( soi.unit_price * soi.amount )\s
                			WHEN 2 THEN
                			( soi.unit_price_invoice * soi.amount )\s
                			WHEN 3 THEN
                			( soi.unit_price_noinvoice * soi.amount )\s
                		END\s
                		) AS amount\s
                	FROM
                		t_sl_orders_item soi
                		LEFT JOIN t_sl_orders so ON soi.orders = so.id
                	WHERE
                		so.oid=?0 and so.is_scheduled = 2 and year(soi.create_date) = ?1
                	GROUP BY
                	soi.sales_relationship
                """;

        Map map=slOrdersDao.findMapByConditionNoPage(sql, new Object[]{org, year});
        if(      map.get("data")==null){
            return new ArrayList();
        }

        return (List<Map>) map.get("data");
    }
}